# Timestamp Fix for PostgreSQL Compatibility

## Problem
The `feathers-authentication-management` package was setting timestamp fields (`verifyExpires`, `resetExpires`) with JavaScript timestamps (milliseconds since epoch), but PostgreSQL expects proper timestamp format.

## Error Message
```
date/time field value out of range: "1748882761074"
```

## Solution Implemented

### 1. Custom addVerification Hook
Created a custom `addVerification` hook in `src/services/users/users.ts` that:
- Generates verification tokens properly
- Sets `verifyExpires` as ISO string instead of milliseconds
- Ensures PostgreSQL compatibility

### 2. Timestamp Conversion Hook
Added `convertTimestamps` hook to handle any remaining timestamp conversion needs.

### 3. Schema Resolvers
Updated user schema resolvers to convert timestamp fields from numbers to ISO strings.

## Code Changes

### Custom addVerification Hook
```javascript
const addVerification = (_path?: string) => {
  return async (context: any) => {
    const { data } = context
    
    if (data) {
      // Generate verification tokens
      const verifyToken = require('crypto').randomBytes(15).toString('hex')
      const verifyShortToken = Math.floor(100000 + Math.random() * 900000).toString()
      
      // Set verification fields with proper timestamp format
      data.isVerified = false
      data.verifyToken = verifyToken
      data.verifyShortToken = verifyShortToken
      data.verifyExpires = new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString() // 5 days
      data.verifyChanges = []
      data.resetAttempts = 0
    }
    
    return context
  }
}
```

### Hook Order
```javascript
create: [
  schemaHooks.validateData(userDataValidator),
  schemaHooks.resolveData(userDataResolver),
  addVerification('auth-management'),  // Custom hook with proper timestamps
  convertTimestamps(),                 // Backup conversion
  populateUserTracking()
],
```

## Testing
To test the fix:

1. Create a user via the API
2. Verify no timestamp errors occur
3. Check that verification fields are properly set

## Expected Behavior
- Users can be created without timestamp errors
- Verification emails are sent automatically
- All auth-management functionality works correctly
- PostgreSQL timestamp fields are properly formatted

## Files Modified
- `src/services/users/users.ts` - Added custom addVerification hook
- `src/services/users/users.schema.ts` - Added timestamp conversion in resolvers

This fix ensures full compatibility between feathers-authentication-management and PostgreSQL timestamp fields.
