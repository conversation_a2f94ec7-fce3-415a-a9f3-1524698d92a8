# Authentication Management Setup

This document describes the feathers-authentication-management configuration implemented in the project.

## Overview

The project now includes email verification, password reset, and identity management functionality using the `feathers-authentication-management` package.

## Features Implemented

### 1. Email Verification
- New users receive verification emails upon registration
- Users must verify their email before accessing protected features
- Supports both long tokens (for email links) and short tokens (for manual entry)

### 2. Password Reset
- Users can request password reset emails
- Secure token-based password reset process
- Configurable token expiration times

### 3. Identity Management
- Users can change their email addresses with verification
- Password change notifications

## Configuration

### Service Configuration
- **Service Path**: `/auth-management`
- **User Service**: `users`
- **Email Service**: `_mailer`
- **Token Lengths**: 
  - Long tokens: 30 characters
  - Short tokens: 6 digits
- **Expiration Times**:
  - Email verification: 5 days
  - Password reset: 2 hours

### Database Schema
The following fields were added to the `users` table:
- `isVerified` (boolean) - Email verification status
- `verifyToken` (string) - Long verification token
- `verifyShortToken` (string) - Short verification token
- `verifyExpires` (timestamp) - Verification token expiration
- `verifyChanges` (jsonb) - Tracks email changes
- `resetToken` (string) - Password reset token
- `resetShortToken` (string) - Short reset token
- `resetExpires` (timestamp) - Reset token expiration
- `resetAttempts` (integer) - Failed reset attempts counter

## API Endpoints

### POST /auth-management
Available actions:
- `checkUnique` - Check if email is unique
- `resendVerifySignup` - Resend verification email
- `verifySignupLong` - Verify with long token
- `verifySignupShort` - Verify with short token
- `sendResetPwd` - Send password reset email
- `resetPwdLong` - Reset password with long token
- `resetPwdShort` - Reset password with short token
- `passwordChange` - Change password (authenticated)
- `identityChange` - Change email (authenticated)

## Email Templates

The system sends the following types of emails:
1. **Welcome/Verification** - Sent when user registers
2. **Password Reset** - Sent when user requests password reset
3. **Email Verified** - Confirmation of successful verification
4. **Password Changed** - Notification of password change
5. **Email Changed** - Notification of email address change

## Security Features

- Tokens are automatically hashed in the database
- Failed reset attempts are tracked and limited
- Sensitive fields are hidden from external API responses
- Configurable token expiration times
- Email verification required for password reset (configurable)

## Usage Examples

### Check if email is unique
```javascript
POST /auth-management
{
  "action": "checkUnique",
  "value": { "email": "<EMAIL>" }
}
```

### Resend verification email
```javascript
POST /auth-management
{
  "action": "resendVerifySignup",
  "value": { "email": "<EMAIL>" }
}
```

### Verify email with token
```javascript
POST /auth-management
{
  "action": "verifySignupLong",
  "value": "verification-token-here"
}
```

### Request password reset
```javascript
POST /auth-management
{
  "action": "sendResetPwd",
  "value": { "email": "<EMAIL>" }
}
```

### Reset password
```javascript
POST /auth-management
{
  "action": "resetPwdLong",
  "value": {
    "token": "reset-token-here",
    "password": "newpassword123"
  }
}
```

## Integration with User Service

The user service has been enhanced with:
- `addVerification` hook on user creation
- `removeVerification` hook to clean response data
- Automatic verification email sending
- Timestamp conversion for PostgreSQL compatibility

## Testing

Tests have been created in `test/services/auth-management.test.ts` to verify:
- Service registration
- Email uniqueness checking
- Verification email sending
- Token-based verification
- Password reset functionality
- Error handling for invalid tokens

## Configuration Files

- **Service**: `src/services/auth-management/auth-management.ts`
- **Notifier**: `src/services/auth-management/notifier.ts`
- **User Schema**: `src/services/users/users.schema.ts` (updated)
- **User Service**: `src/services/users/users.ts` (updated)
- **Migration**: `migrations/20250527000000_add_auth_management_fields_to_users.ts`
