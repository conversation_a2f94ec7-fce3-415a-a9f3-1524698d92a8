# Email Verification Examples

## Authentication Requirements

The auth-management service now properly handles authentication requirements:

### ✅ **No Authentication Required**
These actions work without being logged in:
- `checkUnique` - Check if email is available
- `resendVerifySignup` - Resend verification email
- `verifySignupLong` - Verify email with long token
- `verifySignupShort` - Verify email with short token
- `sendResetPwd` - Send password reset email
- `resetPwdLong` - Reset password with long token
- `resetPwdShort` - Reset password with short token

### 🔒 **Authentication Required**
These actions require the user to be logged in:
- `passwordChange` - Change password
- `identityChange` - Change email address

## Usage Examples

### 1. User Registration and Verification Flow

```javascript
// Step 1: Create user (no auth required)
const user = await app.service('users').create({
  email: '<EMAIL>',
  password: 'password123'
});
// User receives verification email automatically

// Step 2: Verify email with token from email (no auth required)
const verifyResult = await app.service('auth-management').create({
  action: 'verifySignupLong',
  value: 'token-from-email'
});

// Step 3: Now user can login
const authResult = await app.service('authentication').create({
  strategy: 'local',
  email: '<EMAIL>',
  password: 'password123'
});
```

### 2. Resend Verification Email

```javascript
// If user didn't receive email (no auth required)
await app.service('auth-management').create({
  action: 'resendVerifySignup',
  value: { email: '<EMAIL>' }
});
```

### 3. Password Reset Flow

```javascript
// Step 1: Request password reset (no auth required)
await app.service('auth-management').create({
  action: 'sendResetPwd',
  value: { email: '<EMAIL>' }
});

// Step 2: Reset password with token (no auth required)
await app.service('auth-management').create({
  action: 'resetPwdLong',
  value: {
    token: 'reset-token-from-email',
    password: 'newpassword123'
  }
});
```

### 4. Change Password (Authenticated)

```javascript
// User must be logged in for this
await app.service('auth-management').create({
  action: 'passwordChange',
  value: {
    oldPassword: 'currentpassword',
    password: 'newpassword123'
  }
}, {
  user: authenticatedUser // Must include user context
});
```

## Frontend Integration

### React Example

```jsx
// Verification component
function EmailVerification({ token }) {
  const [loading, setLoading] = useState(false);
  const [verified, setVerified] = useState(false);

  const verifyEmail = async () => {
    setLoading(true);
    try {
      await app.service('auth-management').create({
        action: 'verifySignupLong',
        value: token
      });
      setVerified(true);
    } catch (error) {
      console.error('Verification failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      {!verified ? (
        <button onClick={verifyEmail} disabled={loading}>
          {loading ? 'Verifying...' : 'Verify Email'}
        </button>
      ) : (
        <p>Email verified successfully! You can now log in.</p>
      )}
    </div>
  );
}
```

### Password Reset Component

```jsx
function PasswordReset() {
  const [email, setEmail] = useState('');
  const [token, setToken] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [step, setStep] = useState('request');

  const requestReset = async (e) => {
    e.preventDefault();
    try {
      await app.service('auth-management').create({
        action: 'sendResetPwd',
        value: { email }
      });
      setStep('reset');
    } catch (error) {
      console.error('Reset request failed:', error);
    }
  };

  const resetPassword = async (e) => {
    e.preventDefault();
    try {
      await app.service('auth-management').create({
        action: 'resetPwdLong',
        value: { token, password: newPassword }
      });
      alert('Password reset successful!');
    } catch (error) {
      console.error('Password reset failed:', error);
    }
  };

  return (
    <div>
      {step === 'request' ? (
        <form onSubmit={requestReset}>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter your email"
            required
          />
          <button type="submit">Send Reset Email</button>
        </form>
      ) : (
        <form onSubmit={resetPassword}>
          <input
            type="text"
            value={token}
            onChange={(e) => setToken(e.target.value)}
            placeholder="Enter reset token"
            required
          />
          <input
            type="password"
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
            placeholder="Enter new password"
            required
          />
          <button type="submit">Reset Password</button>
        </form>
      )}
    </div>
  );
}
```

## Error Handling

```javascript
try {
  await app.service('auth-management').create({
    action: 'verifySignupLong',
    value: token
  });
} catch (error) {
  switch (error.code) {
    case 400:
      console.log('Invalid or expired token');
      break;
    case 404:
      console.log('User not found');
      break;
    case 401:
      console.log('Authentication required for this action');
      break;
    default:
      console.log('Unexpected error:', error.message);
  }
}
```

## Key Points

1. **Email verification does NOT require authentication** - users can verify their email before logging in
2. **Password reset does NOT require authentication** - users can reset forgotten passwords
3. **Password change DOES require authentication** - users must be logged in to change passwords
4. **The conditional authentication hook automatically handles these requirements**
5. **All verification tokens are secure and time-limited**
