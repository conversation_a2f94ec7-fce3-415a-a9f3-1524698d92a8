import fs from 'fs';
import path from 'path';

import { parse } from 'csv-parse';

import { app } from '../src/app';
import type { Match, MatchData } from '../src/services/matches/matches.schema';

// Define an interface for Feathers error type
interface FeathersError extends Error {
  code: number;
  className: string;
  data?: any[];
}

/**
 * Convert string to boolean
 */
const parseBool = (value: string | undefined): boolean | undefined => {
  if (value === undefined || value === '' || value === '\\N') return undefined;
  if (value === '0') return false;
  if (value === '1') return true;
  return undefined;
};

/**
 * Parse JSON field from string 
 * Returns the original JSON string if valid, undefined if invalid/empty
 */
const parseJsonField = (value: string | undefined): string | undefined => {
  if (!value || value === '\\N' || value === '-1') return undefined;
  
  try {
    // Just validate that it's valid JSON, but return the original string
    JSON.parse(value);
    return value;
  } catch (error) {
    console.error(`Error parsing JSON value: ${value}`, error);
    return undefined;
  }
};

/**
 * Parse date fields from DD.MM.YYYY to YYYY-MM-DD
 */
const parseDate = (value: string | undefined): string | undefined => {
  if (!value || value === '\\N') return undefined;
  
  // Handle date format DD.MM.YYYY
  if (value.includes('.')) {
    const [day, month, year] = value.split('.');
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }
  
  return value;
};

/**
 * Parse timestamp fields from DD.MM.YYYY HH:MM to ISO 8601 format (YYYY-MM-DDTHH:MM:SS.sssZ)
 */
const parseTimestamp = (value: string | undefined): string | undefined => {
  if (!value || value === '\\N') return undefined;

  console.log('vparseTimestampalue :', value)
  
  // Handle date-time format DD.MM.YYYY HH:MM
  if (value.includes(' ') && value.includes('.')) {
    const [datePart, timePart] = value.split(' ');
    const [day, month, year] = datePart.split('.');
    
    // Format time part - add seconds if missing
    let formattedTime = timePart;
    if (!formattedTime.includes(':')) formattedTime += ':00';
    if (formattedTime.split(':').length === 2) formattedTime += ':00';
    
    // Create ISO 8601 date-time string
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T${formattedTime}.000Z`;
  }
  
  return value;
};

/**
 * Parse number fields, handling special values and ensuring type safety
 */
const parseNumber = (value: string | undefined): number | undefined => {
  if (!value || value === '\\N' || value === '-1' || value === '') return undefined;
  
  const num = Number(value);
  return !isNaN(num) ? num : undefined;
};

/**
 * Main import function for matches from CSV
 */
async function importMatches(csvFilePath: string) {
  const matches = app.service('matches');
  
  // Ensure the path is absolute
  const filePath = path.isAbsolute(csvFilePath) 
    ? csvFilePath 
    : path.join(process.cwd(), csvFilePath);
  
  console.log(`Importing matches from ${filePath}`);
  
  const parser = fs
    .createReadStream(filePath)
    .pipe(parse({
      delimiter: ';',
      columns: true,
      skip_empty_lines: true,
      relax_quotes: true,
      skip_records_with_empty_values: true
    }));
  
  let recordCount = 0;
  let importCount = 0;
  let errorCount = 0;

  for await (const record of parser) {
    recordCount++;
    
    // Skip completely empty rows
    if (Object.values(record).every(v => !v || v === '')) {
      continue;
    }
    
    try {
      // First create the required data according to the schema
      const matchData: MatchData = {
        legacyId: parseNumber(record.id),
        name: record.name || 'Unnamed Match',
        isActive: parseBool(record.is_active) ?? true, // Added isActive as it's required by MatchData
        // equipmentCategories and ageCategories are optional in MatchData, but if provided, should be valid JSON strings or undefined.
        // The parseJsonField handles this.
        equipmentCategories: parseJsonField(record.equipment_categories),
        ageCategories: parseJsonField(record.age_categories),
        // other required fields for MatchData if any should be added here with default or parsed values
      };
      
      // Then add the additional data as a patch
      const additionalData: Partial<Match> = {
        isActive: parseBool(record.is_active) ?? true,
        country: record.country || undefined,
        city: record.city || undefined,
        postcode: record.postcode || undefined,
        address: record.address || undefined,
        phone: record.phone || undefined,
        email: record.email || undefined,
        startDate: parseDate(record.start_date),
        endDate: parseDate(record.end_date),
        registrationEnds: parseTimestamp(record.registration_ends),
        description: record.description || undefined,
        photo: record.photo || undefined,
        matchType: record.match_type || undefined,
        // Keep JSON fields as strings for PostgreSQL JSONB columns
        equipmentCategories: parseJsonField(record.equipment_categories),
        ageCategories: parseJsonField(record.age_categories),
        forWomen: parseBool(record.for_women),
        forMen: parseBool(record.for_men),
        federation: record.federation === '-1' ? undefined : record.federation,
        licenseRequired: parseBool(record.license_requred),
        organizerId: parseNumber(record.organizer_id),
        maxPlayersAmount: parseNumber(record.max_players_amount),
        payments: parseJsonField(record.payments),
        competitionLevel: record.competition_level || undefined,
        international: parseBool(record.international),
        withoutLimits: parseBool(record.without_limits),
        publishAt: parseTimestamp(record.public),
        completed: parseBool(record.completed),
        latitude: record.latitude ? parseFloat(record.latitude.replace(',', '.')) : undefined,
        longitude: record.longitude ? parseFloat(record.longitude.replace(',', '.')) : undefined,
        agenda: parseJsonField(record.agenda),
        currency: record.currency || undefined,
        judges: parseJsonField(record.judges),
        registrationFinished: parseBool(record.registration_finished),
        attachments: parseJsonField(record.attachments),
        createdAt: parseTimestamp(record.created_at),
        // Ensure tournamentId is a proper number or undefined
        tournamentId: parseNumber(record.tournament_id),
        tournamentConfirmed: parseBool(record.tournament_confirmed),
        yearly: parseBool(record.yearly)
      };
      
      // Special handling for values that caused validation errors
      if (additionalData.publishAt === undefined) {
        delete additionalData.publishAt;
      }
      
      if (additionalData.tournamentId === undefined) {
        delete additionalData.tournamentId;
      }
      
      // Remove any undefined values to avoid schema validation issues
      Object.keys(additionalData).forEach(key => {
        if (additionalData[key as keyof Match] === undefined) {
          delete additionalData[key as keyof Match];
        }
      });
      
      // Debug for fields that caused validation errors
      if (record.public) {
        console.log(`Record #${recordCount} - publishAt field: ${record.public} -> ${additionalData.publishAt || 'removed'}`);
      }
      
      if (record.tournament_id) {
        console.log(`Record #${recordCount} - tournamentId: ${record.tournament_id} -> ${additionalData.tournamentId || 'removed'}`);
      }
      
      // Create the match with required fields first, then update with additional data
      const createdMatch = await matches.create(matchData);
      if (Object.keys(additionalData).length > 0) {
        await matches.patch(createdMatch.id, additionalData);
      }
      
      importCount++;
      console.log(`Imported match #${importCount}: ${matchData.name}`);
    } catch (error: unknown) {
      errorCount++;
      console.error(`Error importing record #${recordCount}:`, error);
      
      // Enhanced error logging with proper type checking
      if (error && typeof error === 'object' && 'data' in error) {
        const feathersError = error as FeathersError;
        if (Array.isArray(feathersError.data) && feathersError.data.length > 0) {
          console.error('Validation error details:', JSON.stringify(feathersError.data, null, 2));
          console.error('Problematic record data:', JSON.stringify({
            name: record.name,
            publishAt: record.public,
            tournamentId: record.tournament_id
          }, null, 2));
        }
      }
    }
  }
  
  console.log(`Import complete: ${importCount} matches imported successfully, ${errorCount} errors`);
}

// If this script is run directly (not imported)
if (require.main === module) {
  const csvFilePath = process.argv[2];
  if (!csvFilePath) {
    console.error('Error: No CSV file path provided');
    console.log('Usage: pnpm exec ts-node scripts/import-matches.ts <path/to/csv/file>');
    process.exit(1);
  }
  
  // Run the import
  importMatches(csvFilePath)
    .then(() => process.exit(0))
    .catch(error => {
      console.error('Import failed:', error);
      process.exit(1);
    });
}

export { importMatches };