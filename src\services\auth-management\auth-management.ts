import { AuthenticationManagementService } from 'feathers-authentication-management'
import { createSwaggerServiceOptions } from 'feathers-swagger'

import { authenticate } from '@feathersjs/authentication'
import { Type } from '@feathersjs/typebox'

import type { Application } from '../../declarations'
import { notifier } from './notifier'

// Schema for auth management requests
const authManagementDataSchema = Type.Object({
  action: Type.String({
    enum: [
      'checkUnique',
      'resendVerifySignup', 
      'verifySignupLong',
      'verifySignupShort',
      'verifySignupSetPasswordLong',
      'verifySignupSetPasswordShort',
      'sendResetPwd',
      'resetPwdLong',
      'resetPwdShort',
      'passwordChange',
      'identityChange'
    ]
  }),
  value: Type.Optional(Type.Union([
    Type.String(),
    Type.Object({}, { additionalProperties: true })
  ])),
  notifierOptions: Type.Optional(Type.Object({}, { additionalProperties: true }))
}, { $id: 'AuthManagementData', additionalProperties: true })

// Schema for auth management responses
const authManagementResponseSchema = Type.Object({
  success: Type.Boolean(),
  message: Type.Optional(Type.String()),
  user: Type.Optional(Type.Object({}, { additionalProperties: true }))
}, { $id: 'AuthManagementResponse', additionalProperties: true })

declare module '../../declarations' {
  interface ServiceTypes {
    'auth-management': AuthenticationManagementService
  }
}

// Hook to conditionally require authentication based on action
const conditionalAuthenticate = () => {
  return async (context: any) => {
    const { data } = context

    // Actions that require authentication
    const authRequiredActions = ['passwordChange', 'identityChange']

    if (data && authRequiredActions.includes(data.action)) {
      // Apply authentication for these actions
      return authenticate('jwt')(context)
    }

    // No authentication required for other actions
    return context
  }
}

export const authManagement = (app: Application) => {
  const authManagementService = new AuthenticationManagementService(app, {
    // Service path for users
    service: 'users',

    // Don't skip email verification check for password reset
    skipIsVerifiedCheck: false,

    // Sanitize user object in responses (remove sensitive fields)
    sanitizeUserForClient: (user: any) => {
      const {
        password,
        verifyToken,
        verifyShortToken,
        verifyExpires,
        verifyChanges,
        resetToken,
        resetShortToken,
        resetExpires,
        resetAttempts,
        ...sanitizedUser
      } = user
      return sanitizedUser
    },

    // Notifier function for sending emails
    notifier: notifier(app),

    // Token configuration
    longTokenLen: 15,    // 30 character tokens
    shortTokenLen: 6,    // 6 character tokens
    shortTokenDigits: true, // Use only digits for short tokens

    // Token expiration times - return Date objects instead of milliseconds
    delay: 5 * 24 * 60 * 60 * 1000,      // 5 days for email verification
    resetDelay: 2 * 60 * 60 * 1000,      // 2 hours for password reset

    // Reset attempts before token invalidation
    resetAttempts: 5,

    // Don't reuse reset tokens
    reuseResetToken: false,

    // Properties that uniquely identify a user
    identifyUserProps: ['email'],

    // Password field name
    passwordField: 'password',

    // Pass params from auth-management to users service
    passParams: (params: any) => params
  })

  // Register the service
  app.use('auth-management', authManagementService, {
    docs: createSwaggerServiceOptions({
      schemas: {
        authManagementDataSchema,
        authManagementResponseSchema
      },
      docs: {
        description: `Authentication Management Service
        
Handles user email verification, password reset, and identity management.

Available actions:
- checkUnique: Check if email/username is unique
- resendVerifySignup: Resend verification email
- verifySignupLong: Verify email with long token
- verifySignupShort: Verify email with short token  
- verifySignupSetPasswordLong: Verify email and set password with long token
- verifySignupSetPasswordShort: Verify email and set password with short token
- sendResetPwd: Send password reset email
- resetPwdLong: Reset password with long token
- resetPwdShort: Reset password with short token
- passwordChange: Change password (requires authentication)
- identityChange: Change email address (requires authentication)`,
        securities: ['create'],
        tags: ['auth']
      }
    })
  })

  // Add conditional authentication hook
  app.service('auth-management').hooks({
    around: {
      all: []
    },
    before: {
      all: [],
      create: [conditionalAuthenticate()]
    },
    after: {
      all: [],
      create: []
    },
    error: {
      all: [],
      create: []
    }
  })
}
