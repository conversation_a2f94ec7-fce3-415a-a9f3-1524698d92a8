# Archery Points - Sports Tracking App

## Project Overview

This is a sports tracking application API built with Feathers:

- Tracking matches results and statistics
- Users to sign up for matches
- Competition organizers to create and manage events

## Tech Stack

- Feathers.js
- TypeScript
- typebox
- Koa
- Knex
- axios
- pnpm (also use pnpm dlx intead npx)
- postgres DB


## Coding Conventions

## Best Practices

- Generate comments only complex logic
- Use TypeScript for type safety
- Follow the Single Responsibility Principle
- Use async/await for asynchronous operations
- Implement proper error handling
- use JSONB for objects, arrays or complex data in DB
- use camelCase for column names

## Common rules
- if needed, run tests yourself and validate results.

## DB models
- always implement createdAt, updatedAt, deletedAt, createdBy, updatedBy, deletedBy columns
- create migration file when you change schema, but never run migrations by yourself
- do not use cli for creating migrations, instead generate required file and fill it manually
