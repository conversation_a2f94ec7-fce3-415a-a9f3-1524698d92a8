import { removeVerification } from 'feathers-authentication-management'
import { createSwaggerServiceOptions } from 'feathers-swagger'

// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html
import { authenticate } from '@feathersjs/authentication'
import { hooks as schemaHooks } from '@feathersjs/schema'

import type { Application } from '../../declarations'
import { populateUserTracking, skipIfDeletedByHook } from '../../hooks/user-tracking'
import { UserService, getOptions } from './users.class'
import {
  userDataResolver,
  userDataSchema,
  userDataValidator,
  userExternalResolver,
  userPatchResolver,
  userPatchSchema,
  userPatchValidator,
  userQueryResolver,
  userQuerySchema,
  userQueryValidator,
  userResolver,
  userSchema
} from './users.schema'
import { userMethods, userPath } from './users.shared'

export * from './users.class'
export * from './users.schema'

// Custom addVerification hook that handles PostgreSQL timestamps properly
const addVerification = (_path?: string) => {
  return async (context: any) => {
    const { data } = context

    if (data) {
      // Generate verification tokens
      const verifyToken = require('crypto').randomBytes(15).toString('hex')
      const verifyShortToken = Math.floor(100000 + Math.random() * 900000).toString()

      // Set verification fields with proper timestamp format
      data.isVerified = false
      data.verifyToken = verifyToken
      data.verifyShortToken = verifyShortToken
      data.verifyExpires = new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString() // 5 days
      data.verifyChanges = []
      data.resetAttempts = 0
    }

    return context
  }
}

// Hook to convert timestamp fields from milliseconds to ISO strings for PostgreSQL compatibility
const convertTimestamps = () => {
  return async (context: any) => {
    const { data } = context

    if (data) {
      // Convert verifyExpires if it's a number (milliseconds)
      if (typeof data.verifyExpires === 'number') {
        data.verifyExpires = new Date(data.verifyExpires).toISOString()
        console.log('Converted verifyExpires:', data.verifyExpires)
      }

      // Convert resetExpires if it's a number (milliseconds)
      if (typeof data.resetExpires === 'number') {
        data.resetExpires = new Date(data.resetExpires).toISOString()
        console.log('Converted resetExpires:', data.resetExpires)
      }

      // Also check for these fields in nested objects or arrays
      if (Array.isArray(data)) {
        data.forEach(item => {
          if (typeof item.verifyExpires === 'number') {
            item.verifyExpires = new Date(item.verifyExpires).toISOString()
          }
          if (typeof item.resetExpires === 'number') {
            item.resetExpires = new Date(item.resetExpires).toISOString()
          }
        })
      }
    }

    return context
  }
}

// Hook to send verification email after user creation
const sendVerificationEmail = () => {
  return async (context: any) => {
    const { app, result } = context
    const authManagementService = app.service('auth-management')

    if (!authManagementService) {
      console.warn('Auth management service not available, skipping verification email')
      return context
    }

    const users = Array.isArray(result) ? result : [result]

    // Send verification email for each created user
    await Promise.all(
      users.map(async (user: any) => {
        if (user && user.email && !user.isVerified) {
          try {
            await authManagementService.create({
              action: 'resendVerifySignup',
              value: { email: user.email }
            })
          } catch (error) {
            console.error('Failed to send verification email:', error)
            // Don't fail user creation if email fails
          }
        }
      })
    )

    return context
  }
}

// A configure function that registers the service and its hooks via `app.configure`
export const user = (app: Application) => {
  // Register our service on the Feathers application
  app.use(userPath, new UserService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: userMethods,
    // You can add additional custom events to be sent to clients here
    events: [],
    docs: createSwaggerServiceOptions({
      schemas: { userDataSchema, userQuerySchema, userSchema, userPatchSchema },
      docs: {
        description: 'User service',
        securities: ['all']
      }
    })
  })
  // Initialize hooks
  app.service(userPath).hooks({
    around: {
      all: [schemaHooks.resolveExternal(userExternalResolver), schemaHooks.resolveResult(userResolver)],
      find: [authenticate('jwt')],
      get: [authenticate('jwt')],
      create: [],
      update: [authenticate('jwt')],
      patch: [authenticate('jwt')],
      remove: [authenticate('jwt')]
    },
    before: {
      all: [skipIfDeletedByHook(), schemaHooks.validateQuery(userQueryValidator), schemaHooks.resolveQuery(userQueryResolver)],
      find: [],
      get: [],
      create: [
        schemaHooks.validateData(userDataValidator),
        schemaHooks.resolveData(userDataResolver),
        addVerification('auth-management'),
        convertTimestamps(),
        populateUserTracking()
      ],
      patch: [
        schemaHooks.validateData(userPatchValidator),
        schemaHooks.resolveData(userPatchResolver),
        convertTimestamps(),
        populateUserTracking()
      ],
      remove: [populateUserTracking()]
    },
    after: {
      all: [],
      create: [
        removeVerification(),
        sendVerificationEmail()
      ]
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    [userPath]: UserService
  }
}
