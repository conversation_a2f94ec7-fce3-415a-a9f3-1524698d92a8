import { createSwaggerServiceOptions } from 'feathers-swagger'

// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html
import { authenticate } from '@feathersjs/authentication'
import { hooks as schemaHooks } from '@feathersjs/schema'
import { addVerification, removeVerification } from 'feathers-authentication-management'

import type { Application } from '../../declarations'
import { populateUserTracking, skipIfDeletedByHook } from '../../hooks/user-tracking'
import { UserService, getOptions } from './users.class'
import {
  userDataResolver,
  userDataSchema,
  userDataValidator,
  userExternalResolver,
  userPatchResolver,
  userPatchSchema,
  userPatchValidator,
  userQueryResolver,
  userQuerySchema,
  userQueryValidator,
  userResolver,
  userSchema
} from './users.schema'
import { userMethods, userPath } from './users.shared'

export * from './users.class'
export * from './users.schema'

// Hook to send verification email after user creation
const sendVerificationEmail = () => {
  return async (context: any) => {
    const { app, result } = context
    const authManagementService = app.service('auth-management')

    if (!authManagementService) {
      console.warn('Auth management service not available, skipping verification email')
      return context
    }

    const users = Array.isArray(result) ? result : [result]

    // Send verification email for each created user
    await Promise.all(
      users.map(async (user: any) => {
        if (user && user.email && !user.isVerified) {
          try {
            await authManagementService.create({
              action: 'resendVerifySignup',
              value: { email: user.email }
            })
          } catch (error) {
            console.error('Failed to send verification email:', error)
            // Don't fail user creation if email fails
          }
        }
      })
    )

    return context
  }
}

// A configure function that registers the service and its hooks via `app.configure`
export const user = (app: Application) => {
  // Register our service on the Feathers application
  app.use(userPath, new UserService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: userMethods,
    // You can add additional custom events to be sent to clients here
    events: [],
    docs: createSwaggerServiceOptions({
      schemas: { userDataSchema, userQuerySchema, userSchema, userPatchSchema },
      docs: {
        description: 'User service',
        securities: ['all']
      }
    })
  })
  // Initialize hooks
  app.service(userPath).hooks({
    around: {
      all: [schemaHooks.resolveExternal(userExternalResolver), schemaHooks.resolveResult(userResolver)],
      find: [authenticate('jwt')],
      get: [authenticate('jwt')],
      create: [],
      update: [authenticate('jwt')],
      patch: [authenticate('jwt')],
      remove: [authenticate('jwt')]
    },
    before: {
      all: [skipIfDeletedByHook(), schemaHooks.validateQuery(userQueryValidator), schemaHooks.resolveQuery(userQueryResolver)],
      find: [],
      get: [],
      create: [
        schemaHooks.validateData(userDataValidator),
        schemaHooks.resolveData(userDataResolver),
        addVerification('auth-management'),
        populateUserTracking()
      ],
      patch: [schemaHooks.validateData(userPatchValidator), schemaHooks.resolveData(userPatchResolver), populateUserTracking()],
      remove: [populateUserTracking()]
    },
    after: {
      all: [],
      create: [
        removeVerification(),
        sendVerificationEmail()
      ]
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    [userPath]: UserService
  }
}
