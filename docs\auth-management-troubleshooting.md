# Auth Management Troubleshooting

## Current Issue: "Not authenticated" Error

If you're still getting a "Not authenticated" error when trying to use the auth-management service, here are the steps to troubleshoot and resolve it.

## Quick Test

Try this simple request to test if the service is working:

```bash
curl -X POST http://localhost:3030/auth-management \
  -H "Content-Type: application/json" \
  -d '{
    "action": "checkUnique",
    "value": { "email": "<EMAIL>" }
  }'
```

Expected response:
```json
{
  "success": true
}
```

If you get a 401 "Not authenticated" error, there's still an authentication requirement somewhere.

## Possible Causes and Solutions

### 1. Global Authentication Hook

Check if there's a global authentication hook in your app. Look for:

```javascript
// In app.ts or similar
app.hooks({
  before: {
    all: [authenticate('jwt')] // This would require auth for ALL services
  }
})
```

**Solution**: Exclude auth-management from global auth hooks:

```javascript
app.hooks({
  before: {
    all: [
      (context) => {
        // Skip authentication for auth-management service
        if (context.path === 'auth-management') {
          return context;
        }
        return authenticate('jwt')(context);
      }
    ]
  }
})
```

### 2. Service Registration Issue

The auth-management service might not be registered correctly.

**Check**: Verify the service is registered:
```javascript
console.log('Auth service exists:', !!app.service('auth-management'));
```

### 3. Feathers Authentication Management Internal Issue

The `feathers-authentication-management` package might have internal authentication requirements.

**Solution**: Create a custom auth-management service:

```javascript
// src/services/auth-management/custom-auth-management.ts
import { Id, Params } from '@feathersjs/feathers';

export class CustomAuthManagementService {
  constructor(private app: any) {}

  async create(data: any, params?: Params) {
    const { action, value } = data;
    const userService = this.app.service('users');

    switch (action) {
      case 'checkUnique':
        const existing = await userService.find({
          query: { email: value.email }
        });
        return { success: existing.total === 0 };

      case 'verifySignupLong':
        const user = await userService.find({
          query: { verifyToken: value }
        });
        if (user.total === 0) {
          throw new Error('Invalid token');
        }
        await userService.patch(user.data[0].id, {
          isVerified: true,
          verifyToken: null,
          verifyExpires: null
        });
        return { user: user.data[0] };

      case 'resendVerifySignup':
        // Implementation for resending verification
        return { success: true };

      default:
        throw new Error(`Unknown action: ${action}`);
    }
  }
}
```

### 4. Frontend Request Issue

Make sure your frontend request doesn't include authentication headers for verification actions:

```javascript
// ❌ Wrong - includes auth header
fetch('/auth-management', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token  // Remove this for verification
  },
  body: JSON.stringify({
    action: 'verifySignupLong',
    value: token
  })
});

// ✅ Correct - no auth header for verification
fetch('/auth-management', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    action: 'verifySignupLong',
    value: token
  })
});
```

## Current Configuration Status

The auth-management service is currently configured with:
- ✅ No authentication hooks
- ✅ Proper service registration
- ✅ PostgreSQL timestamp compatibility
- ✅ Email notification system

## Testing Steps

1. **Test service registration**:
   ```bash
   curl http://localhost:3030/auth-management
   ```

2. **Test checkUnique action**:
   ```bash
   curl -X POST http://localhost:3030/auth-management \
     -H "Content-Type: application/json" \
     -d '{"action": "checkUnique", "value": {"email": "<EMAIL>"}}'
   ```

3. **Create a user and test verification**:
   ```bash
   # Create user
   curl -X POST http://localhost:3030/users \
     -H "Content-Type: application/json" \
     -d '{"email": "<EMAIL>", "password": "password123"}'

   # Get user to see verification token
   curl http://localhost:3030/users/USER_ID

   # Test verification
   curl -X POST http://localhost:3030/auth-management \
     -H "Content-Type: application/json" \
     -d '{"action": "verifySignupLong", "value": "VERIFICATION_TOKEN"}'
   ```

## Next Steps

If you're still experiencing the authentication issue:

1. Share the exact request you're making
2. Share the full error response
3. Check the server logs for any additional error details
4. Consider implementing the custom auth-management service as a fallback

The auth-management service should work without authentication for verification actions. If it doesn't, there's likely a configuration issue that needs to be identified and resolved.
