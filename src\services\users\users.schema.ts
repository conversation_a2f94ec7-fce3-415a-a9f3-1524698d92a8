import crypto from 'crypto'

import { passwordHash } from '@feathersjs/authentication-local'
// // For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'

import type { HookContext } from '../../declarations'
import { dataValidator, queryValidator } from '../../validators'
import type { UserService } from './users.class'

// Main data model schema
export const userSchema = Type.Object(
  {
    id: Type.Number(),
    email: Type.String(),
    password: Type.Optional(Type.String()),
    googleId: Type.Optional(Type.String()),
    facebookId: Type.Optional(Type.String()),
    githubId: Type.Optional(Type.String()),
    avatar: Type.Optional(Type.String()),
    isActive: Type.Optional(Type.Boolean()),
    activatedAt: Type.Optional(Type.String({ format: 'date-time' })),
    // Authentication management fields
    isVerified: Type.Optional(Type.Boolean()),
    verifyToken: Type.Optional(Type.String()),
    verifyShortToken: Type.Optional(Type.String()),
    verifyExpires: Type.Optional(Type.String({ format: 'date-time' })),
    verifyChanges: Type.Optional(Type.Array(Type.String())),
    resetToken: Type.Optional(Type.String()),
    resetShortToken: Type.Optional(Type.String()),
    resetExpires: Type.Optional(Type.String({ format: 'date-time' })),
    resetAttempts: Type.Optional(Type.Number()),
    createdAt: Type.Optional(Type.String({ format: 'date-time' })),
    updatedAt: Type.Optional(Type.String({ format: 'date-time' })),
    deletedAt: Type.Optional(Type.String({ format: 'date-time' })),
    updatedBy: Type.Optional(Type.Number()),
    deletedBy: Type.Optional(Type.Number())
  },
  { $id: 'User', additionalProperties: false }
)
export type User = Static<typeof userSchema>
export const userValidator = getValidator(userSchema, dataValidator)
export const userResolver = resolve<User, HookContext<UserService>>({})

export const userExternalResolver = resolve<User, HookContext<UserService>>({
  // The password should never be visible externally
  password: async () => undefined,
  // Authentication management fields should not be visible externally
  verifyToken: async () => undefined,
  verifyShortToken: async () => undefined,
  verifyExpires: async () => undefined,
  verifyChanges: async () => undefined,
  resetToken: async () => undefined,
  resetShortToken: async () => undefined,
  resetExpires: async () => undefined,
  resetAttempts: async () => undefined
})

// Schema for creating new entries
export const userDataSchema = Type.Pick(userSchema, ['email', 'password', 'googleId', 'facebookId', 'githubId', 'avatar', 'isActive', 'activatedAt', 'isVerified'], {
  $id: 'UserData'
})
export type UserData = Static<typeof userDataSchema>
export const userDataValidator = getValidator(userDataSchema, dataValidator)
export const userDataResolver = resolve<User, HookContext<UserService>>({
  createdAt: async () => {
    return new Date().toISOString()
  },
  updatedAt: async () => {
    return new Date().toISOString()
  },
  password: passwordHash({ strategy: 'local' }),
  avatar: async (value, user) => {
    // If the user passed an avatar image, use it
    if (value !== undefined) {
      return value
    }

    // Gravatar uses MD5 hashes from an email address to get the image
    const hash = crypto.createHash('md5').update(user.email.toLowerCase()).digest('hex')
    // Return the full avatar URL
    return `https://s.gravatar.com/avatar/${hash}?s=60`
  },
  // Convert timestamp fields from milliseconds to ISO string for auth management
  verifyExpires: async (value) => {
    if (typeof value === 'number') {
      return new Date(value).toISOString()
    }
    return value
  },
  resetExpires: async (value) => {
    if (typeof value === 'number') {
      return new Date(value).toISOString()
    }
    return value
  }
})

// Schema for updating existing entries
export const userPatchSchema = Type.Partial(userSchema, {
  $id: 'UserPatch'
})
export type UserPatch = Static<typeof userPatchSchema>
export const userPatchValidator = getValidator(userPatchSchema, dataValidator)
export const userPatchResolver = resolve<User, HookContext<UserService>>({
  updatedAt: async () => {
    return new Date().toISOString()
  },
  password: passwordHash({ strategy: 'local' })
})

// Schema for allowed query properties
export const userQueryProperties = Type.Pick(userSchema, ['id', 'email', 'googleId', 'facebookId', 'githubId', 'avatar'])
export const userQuerySchema = Type.Intersect(
  [
    querySyntax(userQueryProperties),
    // Add additional query properties here
    Type.Object({}, { additionalProperties: false })
  ],
  { additionalProperties: false }
)
export type UserQuery = Static<typeof userQuerySchema>
export const userQueryValidator = getValidator(userQuerySchema, queryValidator)
export const userQueryResolver = resolve<UserQuery, HookContext<UserService>>({
  // If there is a user (e.g. with authentication), they are only allowed to see their own data
  id: async (value, _user, context) => {
    if (context.params.user) {
       // We want to be able to get a list of all users but
    // only let a user modify their own data otherwise
    if (context.params.user && context.method !== 'find') {
      return context.params.user.id
    }
    }

    return value
  }
})
