# Authentication Management Examples

This document provides practical examples of how to use the authentication management features.

## Frontend Integration Examples

### 1. User Registration with Email Verification

```javascript
// Register a new user
const user = await app.service('users').create({
  email: '<EMAIL>',
  password: 'securepassword123'
});

// User will automatically receive a verification email
// The user object will have isVerified: false initially
console.log('User created:', user.id, 'Verified:', user.isVerified);
```

### 2. Email Verification Process

```javascript
// When user clicks verification link or enters code
const result = await app.service('auth-management').create({
  action: 'verifySignupLong',
  value: 'verification-token-from-email'
});

// Or for short token verification
const result = await app.service('auth-management').create({
  action: 'verifySignupShort',
  value: {
    email: '<EMAIL>',
    token: '123456' // 6-digit code
  }
});

console.log('Verification successful:', result.user.isVerified);
```

### 3. Resend Verification Email

```javascript
// If user didn't receive the email
const result = await app.service('auth-management').create({
  action: 'resendVerifySignup',
  value: { email: '<EMAIL>' }
});

console.log('Verification email resent');
```

### 4. Password Reset Flow

```javascript
// Step 1: Request password reset
const result = await app.service('auth-management').create({
  action: 'sendResetPwd',
  value: { email: '<EMAIL>' }
});

console.log('Password reset email sent');

// Step 2: Reset password with token
const resetResult = await app.service('auth-management').create({
  action: 'resetPwdLong',
  value: {
    token: 'reset-token-from-email',
    password: 'newpassword123'
  }
});

console.log('Password reset successful');
```

### 5. Check Email Uniqueness

```javascript
// Before showing registration form
const uniqueCheck = await app.service('auth-management').create({
  action: 'checkUnique',
  value: { email: '<EMAIL>' }
});

if (uniqueCheck.success) {
  console.log('Email is available');
} else {
  console.log('Email is already taken');
}
```

### 6. Change Password (Authenticated User)

```javascript
// User must be authenticated
const result = await app.service('auth-management').create({
  action: 'passwordChange',
  value: {
    oldPassword: 'currentpassword',
    password: 'newpassword123'
  }
}, {
  user: currentUser // Pass authenticated user context
});

console.log('Password changed successfully');
```

### 7. Change Email Address (Authenticated User)

```javascript
// User must be authenticated
const result = await app.service('auth-management').create({
  action: 'identityChange',
  value: {
    password: 'currentpassword',
    changes: { email: '<EMAIL>' }
  }
}, {
  user: currentUser // Pass authenticated user context
});

console.log('Email change initiated, verification required');
```

## React/Vue Frontend Examples

### React Hook for Email Verification

```javascript
import { useState } from 'react';
import { useFeathers } from './feathers-context';

export function useEmailVerification() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const app = useFeathers();

  const verifyEmail = async (token) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await app.service('auth-management').create({
        action: 'verifySignupLong',
        value: token
      });
      
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const resendVerification = async (email) => {
    setLoading(true);
    setError(null);
    
    try {
      await app.service('auth-management').create({
        action: 'resendVerifySignup',
        value: { email }
      });
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { verifyEmail, resendVerification, loading, error };
}
```

### Password Reset Component

```javascript
import React, { useState } from 'react';
import { useFeathers } from './feathers-context';

export function PasswordResetForm() {
  const [email, setEmail] = useState('');
  const [token, setToken] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [step, setStep] = useState('request'); // 'request' or 'reset'
  const app = useFeathers();

  const requestReset = async (e) => {
    e.preventDefault();
    
    try {
      await app.service('auth-management').create({
        action: 'sendResetPwd',
        value: { email }
      });
      
      setStep('reset');
      alert('Password reset email sent!');
    } catch (error) {
      alert('Error: ' + error.message);
    }
  };

  const resetPassword = async (e) => {
    e.preventDefault();
    
    try {
      await app.service('auth-management').create({
        action: 'resetPwdLong',
        value: { token, password: newPassword }
      });
      
      alert('Password reset successful!');
      // Redirect to login
    } catch (error) {
      alert('Error: ' + error.message);
    }
  };

  if (step === 'request') {
    return (
      <form onSubmit={requestReset}>
        <input
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="Enter your email"
          required
        />
        <button type="submit">Send Reset Email</button>
      </form>
    );
  }

  return (
    <form onSubmit={resetPassword}>
      <input
        type="text"
        value={token}
        onChange={(e) => setToken(e.target.value)}
        placeholder="Enter reset token"
        required
      />
      <input
        type="password"
        value={newPassword}
        onChange={(e) => setNewPassword(e.target.value)}
        placeholder="Enter new password"
        required
      />
      <button type="submit">Reset Password</button>
    </form>
  );
}
```

## Error Handling

```javascript
try {
  const result = await app.service('auth-management').create({
    action: 'verifySignupLong',
    value: token
  });
} catch (error) {
  switch (error.code) {
    case 400:
      console.log('Invalid token or expired');
      break;
    case 404:
      console.log('User not found');
      break;
    default:
      console.log('Unexpected error:', error.message);
  }
}
```

## Best Practices

1. **Always handle errors gracefully** - Show user-friendly messages
2. **Validate input on frontend** - Check email format, password strength
3. **Provide clear feedback** - Show loading states and success messages
4. **Secure token handling** - Don't log tokens, handle them securely
5. **Rate limiting** - Implement rate limiting for sensitive operations
6. **Email deliverability** - Monitor email delivery and handle bounces
