import type { Application } from '../../declarations'
import type { User } from '../users/users.schema'

export interface NotifierOptions {
  from?: string
  subject?: string
  [key: string]: any
}

/**
 * Notifier function for feathers-authentication-management
 * Handles sending emails for various authentication management actions
 */
export const notifier = (app: Application) => {
  return async (type: string, user: any, notifierOptions?: NotifierOptions): Promise<void> => {
    const emailService = app.service('_mailer')
    
    if (!emailService) {
      console.warn('Email service not available, skipping notification')
      return
    }

    const { email, id } = user
    if (!email) {
      console.warn('User has no email address, skipping notification')
      return
    }

    let emailData: any = {
      to: email,
      from: notifierOptions?.from || '<EMAIL>'
    }

    switch (type) {
      case 'resendVerifySignup':
        emailData = {
          ...emailData,
          subject: 'Verify Your Email Address',
          html: `
            <h1>Welcome to Archery Points!</h1>
            <p>Please verify your email address by clicking the link below:</p>
            <p><a href="${getVerificationUrl(app, user.verifyToken)}">Verify Email</a></p>
            <p>Or use this verification code: <strong>${user.verifyShortToken}</strong></p>
            <p>This link will expire in 5 days.</p>
          `,
          text: `Welcome to Archery Points! Please verify your email address by visiting: ${getVerificationUrl(app, user.verifyToken)} or use verification code: ${user.verifyShortToken}. This link will expire in 5 days.`
        }
        break

      case 'verifySignup':
        emailData = {
          ...emailData,
          subject: 'Email Verified Successfully',
          html: `
            <h1>Email Verified!</h1>
            <p>Your email address has been successfully verified.</p>
            <p>You can now access all features of Archery Points.</p>
          `,
          text: 'Your email address has been successfully verified. You can now access all features of Archery Points.'
        }
        break

      case 'verifySignupSetPassword':
        emailData = {
          ...emailData,
          subject: 'Account Verified and Password Set',
          html: `
            <h1>Account Setup Complete!</h1>
            <p>Your email has been verified and your password has been set.</p>
            <p>You can now log in to Archery Points.</p>
          `,
          text: 'Your email has been verified and your password has been set. You can now log in to Archery Points.'
        }
        break

      case 'sendResetPwd':
        emailData = {
          ...emailData,
          subject: 'Reset Your Password',
          html: `
            <h1>Password Reset Request</h1>
            <p>You requested to reset your password. Click the link below to reset it:</p>
            <p><a href="${getPasswordResetUrl(app, user.resetToken)}">Reset Password</a></p>
            <p>Or use this reset code: <strong>${user.resetShortToken}</strong></p>
            <p>This link will expire in 2 hours.</p>
            <p>If you didn't request this, please ignore this email.</p>
          `,
          text: `You requested to reset your password. Visit: ${getPasswordResetUrl(app, user.resetToken)} or use reset code: ${user.resetShortToken}. This link will expire in 2 hours. If you didn't request this, please ignore this email.`
        }
        break

      case 'resetPwd':
        emailData = {
          ...emailData,
          subject: 'Password Reset Successfully',
          html: `
            <h1>Password Reset Complete</h1>
            <p>Your password has been successfully reset.</p>
            <p>If you didn't make this change, please contact support immediately.</p>
          `,
          text: 'Your password has been successfully reset. If you didn\'t make this change, please contact support immediately.'
        }
        break

      case 'passwordChange':
        emailData = {
          ...emailData,
          subject: 'Password Changed',
          html: `
            <h1>Password Changed</h1>
            <p>Your password has been changed successfully.</p>
            <p>If you didn't make this change, please contact support immediately.</p>
          `,
          text: 'Your password has been changed successfully. If you didn\'t make this change, please contact support immediately.'
        }
        break

      case 'identityChange':
        emailData = {
          ...emailData,
          subject: 'Email Address Changed',
          html: `
            <h1>Email Address Changed</h1>
            <p>Your email address has been changed successfully.</p>
            <p>If you didn't make this change, please contact support immediately.</p>
          `,
          text: 'Your email address has been changed successfully. If you didn\'t make this change, please contact support immediately.'
        }
        break

      default:
        console.warn(`Unknown notification type: ${type}`)
        return
    }

    try {
      await emailService.create(emailData)
      console.log(`Sent ${type} notification to ${email}`)
    } catch (error) {
      console.error(`Failed to send ${type} notification to ${email}:`, error)
      // Don't throw error to prevent breaking the auth flow
    }
  }
}

/**
 * Generate verification URL for email verification
 */
function getVerificationUrl(app: Application, token?: string): string {
  const baseUrl = app.get('host') || 'http://localhost:3030'
  return `${baseUrl}/verify-email?token=${token}`
}

/**
 * Generate password reset URL
 */
function getPasswordResetUrl(app: Application, token?: string): string {
  const baseUrl = app.get('host') || 'http://localhost:3030'
  return `${baseUrl}/reset-password?token=${token}`
}
