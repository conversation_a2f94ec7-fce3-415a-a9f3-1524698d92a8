import assert from 'assert'
import { app } from '../../src/app'

describe('auth-management service', () => {
  let testUser: any
  let authManagementService: any
  let userService: any

  before(async () => {
    authManagementService = app.service('auth-management')
    userService = app.service('users')
  })

  beforeEach(async () => {
    // Create a test user for each test
    testUser = await userService.create({
      email: `test-${Date.now()}@example.com`,
      password: 'testpassword123'
    })
  })

  afterEach(async () => {
    // Clean up test user
    if (testUser && testUser.id) {
      try {
        await userService.remove(testUser.id)
      } catch (error) {
        // User might already be deleted
      }
    }
  })

  it('should be registered', () => {
    assert.ok(authManagementService, 'Auth management service is registered')
  })

  it('should check unique email', async () => {
    const result = await authManagementService.create({
      action: 'checkUnique',
      value: { email: testUser.email }
    })

    assert.strictEqual(result.success, false, 'Email should not be unique')
  })

  it('should check unique email for new email', async () => {
    const result = await authManagementService.create({
      action: 'checkUnique',
      value: { email: '<EMAIL>' }
    })

    assert.strictEqual(result.success, true, 'Email should be unique')
  })

  it('should resend verification email', async () => {
    const result = await authManagementService.create({
      action: 'resendVerifySignup',
      value: { email: testUser.email }
    })

    assert.ok(result, 'Should return result')
    // Note: We can't easily test email sending in unit tests
    // In a real scenario, you might want to mock the email service
  })

  it('should verify signup with long token', async () => {
    // First, resend verification to get a token
    await authManagementService.create({
      action: 'resendVerifySignup',
      value: { email: testUser.email }
    })

    // Get the updated user with verification token
    const updatedUser = await userService.get(testUser.id)
    
    if (updatedUser.verifyToken) {
      const result = await authManagementService.create({
        action: 'verifySignupLong',
        value: updatedUser.verifyToken
      })

      assert.ok(result.user, 'Should return verified user')
      assert.strictEqual(result.user.isVerified, true, 'User should be verified')
    }
  })

  it('should send password reset email', async () => {
    const result = await authManagementService.create({
      action: 'sendResetPwd',
      value: { email: testUser.email }
    })

    assert.ok(result, 'Should return result')
  })

  it('should reset password with long token', async () => {
    // First, send reset password email
    await authManagementService.create({
      action: 'sendResetPwd',
      value: { email: testUser.email }
    })

    // Get the updated user with reset token
    const updatedUser = await userService.get(testUser.id)
    
    if (updatedUser.resetToken) {
      const result = await authManagementService.create({
        action: 'resetPwdLong',
        value: {
          token: updatedUser.resetToken,
          password: 'newpassword123'
        }
      })

      assert.ok(result.user, 'Should return user after password reset')
    }
  })

  it('should handle invalid token gracefully', async () => {
    try {
      await authManagementService.create({
        action: 'verifySignupLong',
        value: 'invalid-token'
      })
      assert.fail('Should have thrown an error')
    } catch (error: any) {
      assert.ok(error.message, 'Should throw error for invalid token')
    }
  })

  it('should handle non-existent email gracefully', async () => {
    try {
      await authManagementService.create({
        action: 'resendVerifySignup',
        value: { email: '<EMAIL>' }
      })
      assert.fail('Should have thrown an error')
    } catch (error: any) {
      assert.ok(error.message, 'Should throw error for non-existent email')
    }
  })

  describe('user creation with verification', () => {
    it('should create user with verification fields', async () => {
      const newUser = await userService.create({
        email: `verification-test-${Date.now()}@example.com`,
        password: 'testpassword123'
      })

      assert.ok(newUser.id, 'User should be created')
      assert.strictEqual(newUser.isVerified, false, 'User should not be verified initially')
      assert.ok(newUser.verifyToken, 'User should have verification token')
      assert.ok(newUser.verifyExpires, 'User should have verification expiry')

      // Clean up
      await userService.remove(newUser.id)
    })
  })
})
